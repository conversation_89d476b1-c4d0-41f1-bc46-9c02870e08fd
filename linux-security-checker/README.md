# Linux等保基线核查工具

基于GB/T 22239-2019网络安全等级保护基本要求开发的Linux系统等保基线核查工具。

## 项目概述

本工具通过SSH远程连接自动化检查Linux服务器是否符合等级保护基线安全要求，提供简洁易用的图形化界面，支持单主机和主机组批量检查，内置等保二级、三级基线检查规则。

## 主要特性

- 🖥️ **图形化界面**: 基于Fyne框架的现代化GUI界面
- 🔐 **SSH远程检查**: 无需在目标服务器安装客户端
- 📋 **等保合规**: 内置等保二级、三级基线检查规则
- 🏢 **批量管理**: 支持单主机和主机组批量检查
- 📊 **多格式报告**: 支持HTML、PDF、Excel格式报告导出
- 🛡️ **安全加固**: 提供详细的安全加固建议

## 技术架构

- **开发语言**: Go 1.21+
- **GUI框架**: Fyne v2.4+
- **SSH连接**: golang.org/x/crypto/ssh
- **数据库**: SQLite 3
- **报告生成**: HTML、PDF、Excel格式
- **部署平台**: Windows、Linux、macOS

## 项目结构

```
linux-security-checker/
├── cmd/                    # 程序入口
├── internal/               # 内部模块
│   ├── gui/               # GUI界面
│   ├── ssh/               # SSH连接
│   ├── database/          # 数据库
│   ├── checker/           # 检查引擎
│   ├── rules/             # 规则定义
│   ├── report/            # 报告生成
│   ├── server/            # 服务器管理
│   └── common/            # 公共组件
├── data/                  # 数据目录
├── templates/             # 报告模板
├── assets/                # 资源文件
├── docs/                  # 文档
└── scripts/               # 构建脚本
```

## 开发状态

当前状态: 项目初始化阶段
- [x] 项目目录结构创建
- [ ] Go模块初始化
- [ ] 依赖安装
- [ ] 开发环境配置

## 开发计划

1. **第一阶段**: 项目初始化和环境搭建
2. **第二阶段**: 核心模块开发
3. **第三阶段**: 检查引擎开发
4. **第四阶段**: GUI界面开发
5. **第五阶段**: 报告系统开发
6. **第六阶段**: 测试和打包发布

## 许可证

本项目采用MIT许可证。

## 联系方式

如有问题或建议，请提交Issue。
