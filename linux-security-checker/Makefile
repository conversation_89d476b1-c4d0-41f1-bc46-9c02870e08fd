# Linux Security Checker Makefile

# Variables
APP_NAME = linux-security-checker
VERSION = 1.0.0
BUILD_DIR = build
DIST_DIR = dist
GO_FILES = $(shell find . -name "*.go" -type f)

# Default target
.PHONY: all
all: build

# Build the application
.PHONY: build
build:
	@echo "Building $(APP_NAME)..."
	@mkdir -p $(BUILD_DIR)
	go build -o $(BUILD_DIR)/$(APP_NAME) ./cmd

# Build for Windows (requires CGO for Fyne and SQLite)
.PHONY: build-windows
build-windows:
	@echo "Building $(APP_NAME) for Windows..."
	@mkdir -p $(BUILD_DIR)
	@echo "Note: This requires a Windows environment or MinGW-w64 cross-compiler"
	@echo "For cross-compilation, ensure x86_64-w64-mingw32-gcc is available"
	CGO_ENABLED=1 GOOS=windows GOARCH=amd64 CC=x86_64-w64-mingw32-gcc go build -ldflags="-H windowsgui" -o $(BUILD_DIR)/$(APP_NAME).exe ./cmd

# Build for Windows (native - run this on Windows)
.PHONY: build-windows-native
build-windows-native:
	@echo "Building $(APP_NAME) for Windows (native)..."
	@mkdir -p $(BUILD_DIR)
	go build -ldflags="-H windowsgui" -o $(BUILD_DIR)/$(APP_NAME).exe ./cmd

# Build for Linux
.PHONY: build-linux
build-linux:
	@echo "Building $(APP_NAME) for Linux..."
	@mkdir -p $(BUILD_DIR)
	GOOS=linux GOARCH=amd64 go build -o $(BUILD_DIR)/$(APP_NAME)-linux ./cmd

# Build for macOS
.PHONY: build-macos
build-macos:
	@echo "Building $(APP_NAME) for macOS..."
	@mkdir -p $(BUILD_DIR)
	GOOS=darwin GOARCH=amd64 go build -o $(BUILD_DIR)/$(APP_NAME)-macos ./cmd

# Build for all platforms
.PHONY: build-all
build-all: build-windows build-linux build-macos

# Package with Fyne
.PHONY: package
package:
	@echo "Packaging $(APP_NAME) with Fyne..."
	@mkdir -p $(DIST_DIR)
	fyne package -o $(DIST_DIR)/$(APP_NAME) ./cmd

# Package for Windows
.PHONY: package-windows
package-windows:
	@echo "Packaging $(APP_NAME) for Windows..."
	@mkdir -p $(DIST_DIR)
	fyne package -os windows -o $(DIST_DIR)/$(APP_NAME).exe ./cmd

# Run the application
.PHONY: run
run:
	go run ./cmd

# Run tests
.PHONY: test
test:
	go test -v ./...

# Run tests with coverage
.PHONY: test-coverage
test-coverage:
	go test -v -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html

# Clean build artifacts
.PHONY: clean
clean:
	@echo "Cleaning build artifacts..."
	rm -rf $(BUILD_DIR)
	rm -rf $(DIST_DIR)
	rm -f coverage.out coverage.html

# Install dependencies
.PHONY: deps
deps:
	@echo "Installing dependencies..."
	go mod download
	go mod tidy

# Format code
.PHONY: fmt
fmt:
	go fmt ./...

# Lint code
.PHONY: lint
lint:
	golangci-lint run

# Initialize project
.PHONY: init
init:
	@echo "Initializing project..."
	go mod init $(APP_NAME)
	go mod tidy

# Help
.PHONY: help
help:
	@echo "Available targets:"
	@echo "  build          - Build the application"
	@echo "  build-windows  - Build for Windows"
	@echo "  build-linux    - Build for Linux"
	@echo "  build-macos    - Build for macOS"
	@echo "  build-all      - Build for all platforms"
	@echo "  package        - Package with Fyne"
	@echo "  package-windows- Package for Windows"
	@echo "  run            - Run the application"
	@echo "  test           - Run tests"
	@echo "  test-coverage  - Run tests with coverage"
	@echo "  clean          - Clean build artifacts"
	@echo "  deps           - Install dependencies"
	@echo "  fmt            - Format code"
	@echo "  lint           - Lint code"
	@echo "  init           - Initialize project"
	@echo "  help           - Show this help"
