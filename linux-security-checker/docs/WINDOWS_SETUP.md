# Windows开发环境配置指南

## 概述

本文档详细说明如何在Windows平台上配置Linux等保基线核查工具的开发环境。

## 系统要求

- Windows 10/11 (64位)
- 至少4GB内存
- 至少2GB可用磁盘空间

## 必需软件安装

### 1. Go语言环境

#### 下载和安装
1. 访问 [Go官网](https://golang.org/dl/)
2. 下载Windows版本的Go 1.21+安装包
3. 运行安装程序，使用默认安装路径 `C:\Go`
4. 安装完成后，重启命令提示符

#### 验证安装
```cmd
go version
```
应该显示类似：`go version go1.21.x windows/amd64`

#### 配置环境变量
确保以下环境变量已设置：
- `GOROOT`: `C:\Go`
- `GOPATH`: `C:\Users\<USER>\go`
- `PATH`: 包含 `C:\Go\bin` 和 `%GOPATH%\bin`

### 2. C编译器 (CGO支持)

由于项目使用SQLite和Fyne GUI，需要CGO支持，必须安装C编译器。

#### 选项1: TDM-GCC (推荐)
1. 访问 [TDM-GCC官网](https://jmeubank.github.io/tdm-gcc/)
2. 下载TDM-GCC 64位版本
3. 安装时选择"Add to PATH"选项

#### 选项2: MinGW-w64
1. 访问 [MinGW-w64官网](https://www.mingw-w64.org/)
2. 下载并安装MinGW-w64
3. 将MinGW的bin目录添加到PATH环境变量

#### 验证C编译器
```cmd
gcc --version
```

### 3. Git版本控制

1. 访问 [Git官网](https://git-scm.com/download/win)
2. 下载并安装Git for Windows
3. 安装时选择"Git from the command line and also from 3rd-party software"

### 4. 代码编辑器 (可选)

推荐使用以下编辑器之一：
- [Visual Studio Code](https://code.visualstudio.com/) + Go扩展
- [GoLand](https://www.jetbrains.com/go/)
- [Sublime Text](https://www.sublimetext.com/) + GoSublime

## 项目构建配置

### 1. 克隆项目
```cmd
git clone <repository-url>
cd linux-security-checker
```

### 2. 安装依赖
```cmd
go mod download
go mod tidy
```

### 3. 安装Fyne命令行工具
```cmd
go install fyne.io/tools/cmd/fyne@latest
```

### 4. 验证Fyne工具
```cmd
fyne version
```

## 构建命令

### 开发环境运行
```cmd
go run cmd/main.go
```

### 构建可执行文件
```cmd
go build -o linux-security-checker.exe ./cmd
```

### 使用Fyne打包
```cmd
fyne package -o linux-security-checker.exe ./cmd
```

### 构建带图标的Windows应用
```cmd
fyne package -os windows -icon assets/icon.ico -o linux-security-checker.exe ./cmd
```

## 常见问题解决

### 1. CGO编译错误
**错误**: `gcc: command not found`
**解决**: 确保已安装C编译器并添加到PATH

### 2. SQLite编译错误
**错误**: `undefined reference to sqlite3_xxx`
**解决**: 确保CGO_ENABLED=1且有可用的C编译器

### 3. Fyne GUI错误
**错误**: `build constraints exclude all Go files`
**解决**: 确保CGO_ENABLED=1，Fyne需要CGO支持

### 4. 依赖下载失败
**错误**: `go: module xxx: Get "https://proxy.golang.org/...": dial tcp: i/o timeout`
**解决**: 配置Go代理
```cmd
go env -w GOPROXY=https://goproxy.cn,direct
go env -w GOSUMDB=sum.golang.google.cn
```

## 开发工作流

### 1. 日常开发
```cmd
# 运行应用
go run cmd/main.go

# 运行测试
go test ./...

# 格式化代码
go fmt ./...

# 检查代码
go vet ./...
```

### 2. 构建发布版本
```cmd
# 构建优化版本
go build -ldflags="-s -w" -o linux-security-checker.exe ./cmd

# 使用Fyne打包（包含资源文件）
fyne package -os windows -o linux-security-checker.exe ./cmd
```

### 3. 调试
- 使用VS Code的Go调试器
- 或使用delve调试器：`go install github.com/go-delve/delve/cmd/dlv@latest`

## 性能优化建议

1. **编译优化**
   ```cmd
   go build -ldflags="-s -w" ./cmd
   ```

2. **减小可执行文件大小**
   ```cmd
   go build -ldflags="-s -w" -trimpath ./cmd
   ```

3. **启用Go模块代理**
   ```cmd
   go env -w GOPROXY=https://goproxy.cn,direct
   ```

## 部署注意事项

1. **依赖检查**: 确保目标Windows系统有必要的运行时库
2. **权限要求**: 应用可能需要管理员权限进行SSH连接
3. **防火墙设置**: 确保SSH端口(22)未被防火墙阻止
4. **杀毒软件**: 某些杀毒软件可能误报，需要添加白名单

## 故障排除

如果遇到构建问题，请检查：
1. Go版本是否为1.21+
2. CGO是否启用 (`go env CGO_ENABLED`)
3. C编译器是否可用 (`gcc --version`)
4. 环境变量是否正确设置
5. 网络连接是否正常（用于下载依赖）

更多帮助请参考项目文档或提交Issue。
