# 开发文档

## 开发环境要求

- Go 1.21+
- Windows 10/11 (主要开发平台)
- Git
- Make (可选)

## 项目结构说明

```
linux-security-checker/
├── cmd/                    # 程序入口点
│   └── main.go            # 主程序文件
├── internal/               # 内部包，不对外暴露
│   ├── gui/               # GUI界面相关
│   │   ├── app.go         # 应用主框架
│   │   ├── dashboard.go   # 仪表板页面
│   │   ├── servers.go     # 服务器管理页面
│   │   ├── checker.go     # 检查页面
│   │   ├── report.go      # 报告页面
│   │   └── settings.go    # 设置页面
│   ├── ssh/               # SSH连接相关
│   │   ├── client.go      # SSH客户端
│   │   ├── pool.go        # 连接池管理
│   │   ├── executor.go    # 远程命令执行
│   │   └── auth.go        # 认证管理
│   ├── database/          # 数据库相关
│   │   ├── db.go          # 数据库连接管理
│   │   ├── models.go      # 数据模型
│   │   ├── migrations.go  # 数据库迁移
│   │   └── repository.go  # 数据访问层
│   ├── checker/           # 检查引擎
│   │   ├── engine.go      # 检查引擎核心
│   │   ├── account.go     # 账户安全检查
│   │   ├── system.go      # 系统配置检查
│   │   ├── network.go     # 网络安全检查
│   │   ├── filesystem.go  # 文件系统检查
│   │   ├── audit.go       # 日志审计检查
│   │   └── update.go      # 安全更新检查
│   ├── rules/             # 内置规则定义
│   │   ├── builtin.go     # 内置规则集合
│   │   ├── level2.go      # 等保二级规则
│   │   ├── level3.go      # 等保三级规则
│   │   └── custom.go      # 自定义规则
│   ├── report/            # 报告生成
│   │   ├── generator.go   # 报告生成器
│   │   ├── html.go        # HTML报告
│   │   ├── pdf.go         # PDF报告
│   │   └── excel.go       # Excel报告
│   ├── server/            # 服务器管理
│   │   ├── manager.go     # 服务器管理器
│   │   ├── group.go       # 服务器分组
│   │   └── monitor.go     # 状态监控
│   └── common/            # 公共组件
│       ├── logger.go      # 日志组件
│       ├── utils.go       # 工具函数
│       └── types.go       # 数据类型
├── data/                  # 数据目录
│   └── backups/          # 数据库备份目录
├── templates/             # 报告模板
│   ├── html/             # HTML模板
│   └── pdf/              # PDF模板
├── assets/                # 资源文件
│   ├── icons/            # 图标文件
│   └── i18n/             # 国际化文件
├── docs/                  # 文档
├── scripts/               # 构建脚本
├── go.mod                 # Go模块文件
├── go.sum                 # Go依赖校验文件
├── Makefile              # 构建配置
└── README.md             # 项目说明
```

## 开发规范

### 代码风格
- 遵循Go官方代码规范
- 使用gofmt格式化代码
- 公开函数必须有注释
- 包级别的文档注释

### 命名规范
- 包名使用小写字母
- 函数名使用驼峰命名法
- 常量使用大写字母和下划线
- 私有成员以小写字母开头

### 错误处理
- 统一错误处理机制
- 使用errors.New()或fmt.Errorf()创建错误
- 不忽略错误返回值

### 日志规范
- 使用结构化日志
- 区分不同日志级别
- 包含足够的上下文信息

## 构建和运行

### 开发环境运行
```bash
go run cmd/main.go
```

### 构建可执行文件
```bash
make build
```

### Windows版本构建
```bash
make build-windows
```

### 运行测试
```bash
make test
```

## 调试指南

### 日志调试
- 使用内置日志组件
- 设置适当的日志级别
- 查看日志文件定位问题

### GUI调试
- 使用Fyne的调试工具
- 检查界面元素状态
- 验证事件处理逻辑

### 数据库调试
- 使用SQLite命令行工具
- 检查数据表结构和数据
- 验证SQL查询语句

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交代码更改
4. 创建Pull Request
5. 代码审查和合并
