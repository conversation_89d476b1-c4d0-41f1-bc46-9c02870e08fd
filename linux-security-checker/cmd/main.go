package main

import (
	"log"

	"fyne.io/fyne/v2/app"
	"fyne.io/fyne/v2/widget"
	"github.com/google/uuid"
	"github.com/jung-kurt/gofpdf"
	"github.com/mattn/go-sqlite3"
	"github.com/sirupsen/logrus"
	"github.com/xuri/excelize/v2"
	"golang.org/x/crypto/ssh"
	"gopkg.in/yaml.v3"
)

func main() {
	log.Println("Linux Security Checker - 等保基线核查工具")
	log.Println("Version: 1.0.0")
	log.Println("Starting application...")

	// Initialize dependencies to ensure they are included in go.mod
	_ = app.New()
	_ = widget.NewLabel("test")
	_ = uuid.New()
	_ = gofpdf.New("P", "mm", "A4", "")
	_ = sqlite3.SQLiteDriver{}
	_ = logrus.New()
	_ = excelize.NewFile()
	_ = ssh.Password("")
	_ = yaml.Marshal

	log.Println("All dependencies loaded successfully")

	// TODO: Initialize application
}
