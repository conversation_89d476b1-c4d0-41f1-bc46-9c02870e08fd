package main

import (
	"log"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/app"
	"fyne.io/fyne/v2/widget"
	"fyne.io/fyne/v2/container"
	"github.com/google/uuid"
	"github.com/jung-kurt/gofpdf"
	"github.com/sirupsen/logrus"
	"github.com/xuri/excelize/v2"
	"golang.org/x/crypto/ssh"
	"gopkg.in/yaml.v3"
)

func main() {
	log.Println("Linux Security Checker - 等保基线核查工具")
	log.Println("Version: 1.0.0")
	log.Println("Starting application...")

	// Initialize Fyne app for Windows GUI
	myApp := app.New()
	myApp.SetMetadata(&app.Metadata{
		Name: "Linux Security Checker",
		ID:   "com.security.linux-checker",
	})

	myWindow := myApp.NewWindow("Linux等保基线核查工具 v1.0")
	myWindow.Resize(fyne.NewSize(800, 600))

	// Create a simple test interface
	hello := widget.NewLabel("Linux等保基线核查工具")
	hello.Alignment = fyne.TextAlignCenter

	content := container.NewVBox(
		hello,
		widget.NewButton("测试按钮", func() {
			log.Println("Button clicked!")
		}),
	)

	myWindow.SetContent(content)

	// Initialize other dependencies to ensure they are included in go.mod
	_ = uuid.New()
	_ = gofpdf.New("P", "mm", "A4", "")
	_ = logrus.New()
	_ = excelize.NewFile()
	_ = ssh.Password("")
	_ = yaml.Marshal

	log.Println("All dependencies loaded successfully")
	log.Println("Starting GUI application...")

	// Show window and run app
	myWindow.ShowAndRun()
}
