package database

// Migration represents a database migration
type Migration struct {
	Version int
	SQL     string
}

// GetMigrations returns all database migrations
func GetMigrations() []Migration {
	return []Migration{
		{
			Version: 1,
			SQL: `
				CREATE TABLE IF NOT EXISTS servers (
					id INTEGER PRIMARY KEY AUTOINCREMENT,
					name TEXT NOT NULL,
					host TEXT NOT NULL,
					port INTEGER NOT NULL DEFAULT 22,
					username TEXT NOT NULL,
					auth_type TEXT NOT NULL,
					password TEXT,
					key_path TEXT,
					description TEXT,
					status TEXT DEFAULT 'offline',
					created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
					updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
				);
			`,
		},
		{
			Version: 2,
			SQL: `
				CREATE TABLE IF NOT EXISTS server_groups (
					id INTEGER PRIMARY KEY AUTOINCREMENT,
					name TEXT NOT NULL,
					description TEXT,
					created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
					updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
				);
			`,
		},
		{
			Version: 3,
			SQL: `
				CREATE TABLE IF NOT EXISTS server_group_members (
					id INTEGER PRIMARY KEY AUTOINCREMENT,
					group_id INTEGER NOT NULL,
					server_id INTEGER NOT NULL,
					created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
					FOREIGN KEY (group_id) REFERENCES server_groups(id),
					FOREIGN KEY (server_id) REFERENCES servers(id)
				);
			`,
		},
		{
			Version: 4,
			SQL: `
				CREATE TABLE IF NOT EXISTS check_sessions (
					id INTEGER PRIMARY KEY AUTOINCREMENT,
					name TEXT NOT NULL,
					type TEXT NOT NULL,
					target_id INTEGER NOT NULL,
					target_name TEXT NOT NULL,
					template TEXT NOT NULL,
					status TEXT DEFAULT 'pending',
					total_rules INTEGER DEFAULT 0,
					passed_rules INTEGER DEFAULT 0,
					failed_rules INTEGER DEFAULT 0,
					error_rules INTEGER DEFAULT 0,
					started_at DATETIME,
					completed_at DATETIME,
					created_at DATETIME DEFAULT CURRENT_TIMESTAMP
				);
			`,
		},
		{
			Version: 5,
			SQL: `
				CREATE TABLE IF NOT EXISTS check_results (
					id INTEGER PRIMARY KEY AUTOINCREMENT,
					session_id INTEGER NOT NULL,
					server_id INTEGER NOT NULL,
					rule_id TEXT NOT NULL,
					status TEXT NOT NULL,
					actual_value TEXT,
					expected TEXT,
					message TEXT,
					suggestion TEXT,
					created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
					FOREIGN KEY (session_id) REFERENCES check_sessions(id),
					FOREIGN KEY (server_id) REFERENCES servers(id)
				);
			`,
		},
	}
}

// RunMigrations executes all pending migrations
func (dm *DatabaseManager) RunMigrations() error {
	// TODO: Implement migration execution logic
	return nil
}
