package report

// ExcelFormatter formats reports as Excel
type ExcelFormatter struct {
	templatePath string
}

// NewExcelFormatter creates a new Excel formatter
func NewExcelFormatter(templatePath string) *ExcelFormatter {
	return &ExcelFormatter{
		templatePath: templatePath,
	}
}

// Format formats report as Excel
func (ef *ExcelFormatter) Format(report *Report) ([]byte, error) {
	// TODO: Implement Excel formatting logic using xlsx library
	return nil, nil
}

// GetExtension returns file extension for Excel format
func (ef *ExcelFormatter) GetExtension() string {
	return ".xlsx"
}
