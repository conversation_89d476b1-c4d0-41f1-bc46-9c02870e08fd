package report

// PDFFormatter formats reports as PDF
type PDFFormatter struct {
	templatePath string
}

// NewPDFFormatter creates a new PDF formatter
func NewPDFFormatter(templatePath string) *PDFFormatter {
	return &PDFFormatter{
		templatePath: templatePath,
	}
}

// Format formats report as PDF
func (pf *PDFFormatter) Format(report *Report) ([]byte, error) {
	// TODO: Implement PDF formatting logic using gofpdf
	return nil, nil
}

// GetExtension returns file extension for PDF format
func (pf *PDFFormatter) GetExtension() string {
	return ".pdf"
}
