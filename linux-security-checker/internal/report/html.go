package report

// HTMLFormatter formats reports as HTML
type HTMLFormatter struct {
	templatePath string
}

// Formatter interface for different report formats
type Formatter interface {
	Format(report *Report) ([]byte, error)
	GetExtension() string
}

// NewHTMLFormatter creates a new HTML formatter
func NewHTMLFormatter(templatePath string) *HTMLFormatter {
	return &HTMLFormatter{
		templatePath: templatePath,
	}
}

// Format formats report as HTML
func (hf *HTMLFormatter) Format(report *Report) ([]byte, error) {
	// TODO: Implement HTML formatting logic
	return nil, nil
}

// GetExtension returns file extension for HTML format
func (hf *HTMLFormatter) GetExtension() string {
	return ".html"
}
