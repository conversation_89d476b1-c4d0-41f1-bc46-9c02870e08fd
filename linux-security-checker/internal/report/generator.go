package report

import "time"

// ReportGenerator generates security check reports
type ReportGenerator struct {
	templatePath string
	outputPath   string
	formatter    Formatter
}

// Report represents a security check report
type Report struct {
	Summary     Summary
	Servers     []ServerReport
	Categories  []CategoryResult
	Details     []CheckResult
	Timestamp   time.Time
}

// Summary represents report summary information
type Summary struct {
	TotalServers   int
	TotalRules     int
	PassedRules    int
	FailedRules    int
	ErrorRules     int
	HighRiskCount  int
	MediumRiskCount int
	LowRiskCount   int
	ComplianceRate float64
}

// ServerReport represents individual server report
type ServerReport struct {
	ServerID    string
	ServerName  string
	Status      string
	Summary     Summary
	SystemInfo  SystemInfo
	Results     []CheckResult
}

// SystemInfo represents server system information
type SystemInfo struct {
	OS           string
	Kernel       string
	Architecture string
	Hostname     string
}

// CategoryResult represents results by category
type CategoryResult struct {
	Category    string
	TotalRules  int
	PassedRules int
	FailedRules int
	ErrorRules  int
}

// CheckResult represents individual check result
type CheckResult struct {
	RuleID      string
	RuleName    string
	Category    string
	Status      string
	ActualValue string
	Expected    string
	Message     string
	Suggestion  string
	Severity    string
	Timestamp   time.Time
}

// NewReportGenerator creates a new report generator
func NewReportGenerator(templatePath, outputPath string) *ReportGenerator {
	return &ReportGenerator{
		templatePath: templatePath,
		outputPath:   outputPath,
	}
}

// GenerateReport generates a complete security report
func (rg *ReportGenerator) GenerateReport(data interface{}) (*Report, error) {
	// TODO: Implement report generation logic
	return nil, nil
}
