package common

// Config represents application configuration
type Config struct {
	Database DatabaseConfig `json:"database"`
	SSH      SSHConfig      `json:"ssh"`
	GUI      GUIConfig      `json:"gui"`
	Report   ReportConfig   `json:"report"`
}

// DatabaseConfig represents database configuration
type DatabaseConfig struct {
	Path           string `json:"path"`
	BackupEnabled  bool   `json:"backup_enabled"`
	BackupInterval int    `json:"backup_interval"` // hours
}

// SSHConfig represents SSH configuration
type SSHConfig struct {
	Timeout        int `json:"timeout"`         // seconds
	MaxConnections int `json:"max_connections"`
	RetryAttempts  int `json:"retry_attempts"`
}

// GUIConfig represents GUI configuration
type GUIConfig struct {
	Theme    string `json:"theme"`    // light, dark, auto
	Language string `json:"language"` // zh, en
	Width    int    `json:"width"`
	Height   int    `json:"height"`
}

// ReportConfig represents report configuration
type ReportConfig struct {
	DefaultFormat string `json:"default_format"` // html, pdf, excel
	OutputPath    string `json:"output_path"`
	IncludeLogs   bool   `json:"include_logs"`
}

// CheckStatus represents check result status
type CheckStatus string

const (
	StatusPass  CheckStatus = "PASS"
	StatusFail  CheckStatus = "FAIL"
	StatusSkip  CheckStatus = "SKIP"
	StatusError CheckStatus = "ERROR"
)

// Severity represents security issue severity
type Severity string

const (
	SeverityHigh   Severity = "high"
	SeverityMedium Severity = "medium"
	SeverityLow    Severity = "low"
)
