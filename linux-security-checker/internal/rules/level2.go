package rules

import "linux-security-checker/internal/checker"

// GetLevel2Rules returns level 2 compliance rules
func GetLevel2Rules() []checker.CheckRule {
	return []checker.CheckRule{
		{
			ID:          "L2-ACC-001",
			Name:        "密码复杂度策略",
			Category:    "账户安全",
			Level:       "level2",
			Description: "检查密码复杂度策略配置",
			Command:     "grep -E '^(minlen|dcredit|ucredit|lcredit|ocredit)' /etc/security/pwquality.conf",
			Expected:    "minlen=8",
			Severity:    "high",
		},
		{
			ID:          "L2-ACC-002",
			Name:        "账户锁定策略",
			Category:    "账户安全",
			Level:       "level2",
			Description: "检查账户锁定策略配置",
			Command:     "grep -E '^(deny|unlock_time)' /etc/security/faillock.conf",
			Expected:    "deny=5",
			Severity:    "medium",
		},
		{
			ID:          "L2-SYS-001",
			Name:        "SSH配置安全",
			Category:    "系统配置",
			Level:       "level2",
			Description: "检查SSH服务安全配置",
			Command:     "grep -E '^(PermitRootLogin|PasswordAuthentication|Protocol)' /etc/ssh/sshd_config",
			Expected:    "PermitRootLogin no",
			Severity:    "high",
		},
		{
			ID:          "L2-NET-001",
			Name:        "防火墙状态",
			Category:    "网络安全",
			Level:       "level2",
			Description: "检查防火墙服务状态",
			Command:     "systemctl is-active firewalld || systemctl is-active ufw",
			Expected:    "active",
			Severity:    "high",
		},
		{
			ID:          "L2-FILE-001",
			Name:        "关键文件权限",
			Category:    "文件系统",
			Level:       "level2",
			Description: "检查关键系统文件权限",
			Command:     "stat -c '%a' /etc/passwd /etc/shadow /etc/group",
			Expected:    "644 640 644",
			Severity:    "high",
		},
	}
}
