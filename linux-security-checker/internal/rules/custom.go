package rules

import "linux-security-checker/internal/checker"

// CustomRuleManager manages custom security rules
type CustomRuleManager struct {
	rules []checker.CheckRule
}

// NewCustomRuleManager creates a new custom rule manager
func NewCustomRuleManager() *CustomRuleManager {
	return &CustomRuleManager{
		rules: make([]checker.CheckRule, 0),
	}
}

// AddRule adds a custom rule
func (crm *CustomRuleManager) AddRule(rule checker.CheckRule) {
	crm.rules = append(crm.rules, rule)
}

// GetRules returns all custom rules
func (crm *CustomRuleManager) GetRules() []checker.CheckRule {
	return crm.rules
}

// RemoveRule removes a custom rule by ID
func (crm *CustomRuleManager) RemoveRule(ruleID string) {
	for i, rule := range crm.rules {
		if rule.ID == ruleID {
			crm.rules = append(crm.rules[:i], crm.rules[i+1:]...)
			break
		}
	}
}
