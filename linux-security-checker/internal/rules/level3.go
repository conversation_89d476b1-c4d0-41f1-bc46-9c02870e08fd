package rules

import "linux-security-checker/internal/checker"

// GetLevel3Rules returns level 3 compliance rules
func GetLevel3Rules() []checker.CheckRule {
	return []checker.CheckRule{
		{
			ID:          "L3-ACC-001",
			Name:        "强密码复杂度策略",
			Category:    "账户安全",
			Level:       "level3",
			Description: "检查强密码复杂度策略配置",
			Command:     "grep -E '^(minlen|dcredit|ucredit|lcredit|ocredit)' /etc/security/pwquality.conf",
			Expected:    "minlen=12",
			Severity:    "high",
		},
		{
			ID:          "L3-ACC-002",
			Name:        "严格账户锁定策略",
			Category:    "账户安全",
			Level:       "level3",
			Description: "检查严格账户锁定策略配置",
			Command:     "grep -E '^(deny|unlock_time)' /etc/security/faillock.conf",
			Expected:    "deny=3",
			Severity:    "high",
		},
		{
			ID:          "L3-SYS-001",
			Name:        "SSH强化配置",
			Category:    "系统配置",
			Level:       "level3",
			Description: "检查SSH服务强化配置",
			Command:     "grep -E '^(PermitRootLogin|PasswordAuthentication|Protocol|MaxAuthTries)' /etc/ssh/sshd_config",
			Expected:    "PermitRootLogin no, PasswordAuthentication no, MaxAuthTries 3",
			Severity:    "high",
		},
		{
			ID:          "L3-NET-001",
			Name:        "网络参数强化",
			Category:    "网络安全",
			Level:       "level3",
			Description: "检查网络安全参数强化配置",
			Command:     "sysctl net.ipv4.ip_forward net.ipv4.conf.all.send_redirects",
			Expected:    "net.ipv4.ip_forward = 0",
			Severity:    "medium",
		},
		{
			ID:          "L3-AUDIT-001",
			Name:        "审计服务配置",
			Category:    "日志审计",
			Level:       "level3",
			Description: "检查审计服务配置状态",
			Command:     "systemctl is-active auditd && auditctl -s",
			Expected:    "active",
			Severity:    "high",
		},
	}
}
