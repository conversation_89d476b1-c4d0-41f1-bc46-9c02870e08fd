package rules

import "linux-security-checker/internal/checker"

// GetBuiltinRules returns all built-in security check rules
func GetBuiltinRules() []checker.CheckRule {
	var rules []checker.CheckRule
	
	// Add level 2 rules
	rules = append(rules, GetLevel2Rules()...)
	
	// Add level 3 rules
	rules = append(rules, GetLevel3Rules()...)
	
	return rules
}

// GetRulesByLevel returns rules for specific compliance level
func GetRulesByLevel(level string) []checker.CheckRule {
	switch level {
	case "level2":
		return GetLevel2Rules()
	case "level3":
		return GetLevel3Rules()
	default:
		return []checker.CheckRule{}
	}
}
