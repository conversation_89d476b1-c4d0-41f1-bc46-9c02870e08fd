package server

import (
	"context"
	"time"
)

// Monitor monitors server status and connectivity
type Monitor struct {
	servers   map[string]*Server
	interval  time.Duration
	ctx       context.Context
	cancel    context.CancelFunc
	callbacks []StatusCallback
}

// StatusCallback is called when server status changes
type StatusCallback func(serverID, oldStatus, newStatus string)

// NewMonitor creates a new server monitor
func NewMonitor(interval time.Duration) *Monitor {
	ctx, cancel := context.WithCancel(context.Background())
	return &Monitor{
		servers:   make(map[string]*Server),
		interval:  interval,
		ctx:       ctx,
		cancel:    cancel,
		callbacks: make([]StatusCallback, 0),
	}
}

// AddServer adds a server to monitoring
func (m *Monitor) AddServer(server *Server) {
	m.servers[server.ID] = server
}

// RemoveServer removes a server from monitoring
func (m *Monitor) RemoveServer(serverID string) {
	delete(m.servers, serverID)
}

// AddStatusCallback adds a status change callback
func (m *Monitor) AddStatusCallback(callback StatusCallback) {
	m.callbacks = append(m.callbacks, callback)
}

// Start starts the monitoring process
func (m *Monitor) Start() {
	go m.monitorLoop()
}

// Stop stops the monitoring process
func (m *Monitor) Stop() {
	m.cancel()
}

// monitorLoop runs the monitoring loop
func (m *Monitor) monitorLoop() {
	ticker := time.NewTicker(m.interval)
	defer ticker.Stop()
	
	for {
		select {
		case <-m.ctx.Done():
			return
		case <-ticker.C:
			m.checkAllServers()
		}
	}
}

// checkAllServers checks status of all monitored servers
func (m *Monitor) checkAllServers() {
	for _, server := range m.servers {
		go m.checkServerStatus(server)
	}
}

// checkServerStatus checks individual server status
func (m *Monitor) checkServerStatus(server *Server) {
	// TODO: Implement actual connectivity check
	oldStatus := server.Status
	newStatus := "online" // Placeholder
	
	if oldStatus != newStatus {
		server.Status = newStatus
		for _, callback := range m.callbacks {
			callback(server.ID, oldStatus, newStatus)
		}
	}
}
