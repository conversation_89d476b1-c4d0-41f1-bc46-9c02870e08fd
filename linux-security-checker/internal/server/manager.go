package server

import (
	"sync"
	"time"
)

// Server represents a managed server
type Server struct {
	ID          string
	Name        string
	Host        string
	Port        int
	Username    string
	AuthType    string  // password, key
	Password    string  // encrypted
	KeyPath     string
	Groups      []string
	Tags        []string
	Status      string  // online, offline, error
	LastCheck   time.Time
}

// ServerManager manages server configurations
type ServerManager struct {
	servers    map[string]*Server
	groups     map[string][]string
	configPath string
	mutex      sync.RWMutex
}

// NewServerManager creates a new server manager
func NewServerManager(configPath string) *ServerManager {
	return &ServerManager{
		servers:    make(map[string]*Server),
		groups:     make(map[string][]string),
		configPath: configPath,
	}
}

// AddServer adds a new server
func (sm *ServerManager) AddServer(server *Server) error {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()
	
	sm.servers[server.ID] = server
	return nil
}

// GetServer retrieves a server by ID
func (sm *ServerManager) GetServer(id string) (*Server, bool) {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()
	
	server, exists := sm.servers[id]
	return server, exists
}

// ListServers returns all servers
func (sm *ServerManager) ListServers() []*Server {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()
	
	servers := make([]*Server, 0, len(sm.servers))
	for _, server := range sm.servers {
		servers = append(servers, server)
	}
	return servers
}

// UpdateServer updates server information
func (sm *ServerManager) UpdateServer(server *Server) error {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()
	
	sm.servers[server.ID] = server
	return nil
}

// DeleteServer removes a server
func (sm *ServerManager) DeleteServer(id string) error {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()
	
	delete(sm.servers, id)
	return nil
}
