package server

import "sync"

// ServerGroup represents a group of servers
type ServerGroup struct {
	ID          string
	Name        string
	Description string
	ServerIDs   []string
}

// GroupManager manages server groups
type GroupManager struct {
	groups map[string]*ServerGroup
	mutex  sync.RWMutex
}

// NewGroupManager creates a new group manager
func NewGroupManager() *GroupManager {
	return &GroupManager{
		groups: make(map[string]*ServerGroup),
	}
}

// CreateGroup creates a new server group
func (gm *GroupManager) CreateGroup(group *ServerGroup) error {
	gm.mutex.Lock()
	defer gm.mutex.Unlock()
	
	gm.groups[group.ID] = group
	return nil
}

// GetGroup retrieves a group by ID
func (gm *GroupManager) GetGroup(id string) (*ServerGroup, bool) {
	gm.mutex.RLock()
	defer gm.mutex.RUnlock()
	
	group, exists := gm.groups[id]
	return group, exists
}

// ListGroups returns all groups
func (gm *GroupManager) ListGroups() []*ServerGroup {
	gm.mutex.RLock()
	defer gm.mutex.RUnlock()
	
	groups := make([]*ServerGroup, 0, len(gm.groups))
	for _, group := range gm.groups {
		groups = append(groups, group)
	}
	return groups
}

// AddServerToGroup adds a server to a group
func (gm *GroupManager) AddServerToGroup(groupID, serverID string) error {
	gm.mutex.Lock()
	defer gm.mutex.Unlock()
	
	if group, exists := gm.groups[groupID]; exists {
		group.ServerIDs = append(group.ServerIDs, serverID)
	}
	return nil
}

// RemoveServerFromGroup removes a server from a group
func (gm *GroupManager) RemoveServerFromGroup(groupID, serverID string) error {
	gm.mutex.Lock()
	defer gm.mutex.Unlock()
	
	if group, exists := gm.groups[groupID]; exists {
		for i, id := range group.ServerIDs {
			if id == serverID {
				group.ServerIDs = append(group.ServerIDs[:i], group.ServerIDs[i+1:]...)
				break
			}
		}
	}
	return nil
}
