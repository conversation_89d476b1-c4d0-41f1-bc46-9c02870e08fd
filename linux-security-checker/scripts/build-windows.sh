#!/bin/bash

# Windows构建脚本
# 用于在Linux环境下交叉编译Windows版本

set -e

echo "开始构建Windows版本..."

# 检查必要的工具
if ! command -v go &> /dev/null; then
    echo "错误: Go未安装"
    exit 1
fi

# 设置构建参数
export GOOS=windows
export GOARCH=amd64
export CGO_ENABLED=1

# 检查是否有Windows交叉编译工具链
if ! command -v x86_64-w64-mingw32-gcc &> /dev/null; then
    echo "警告: 未找到Windows交叉编译工具链"
    echo "尝试安装: sudo apt-get install gcc-mingw-w64"
    echo "或者在Windows环境下直接编译"
    
    # 尝试不使用CGO编译（会失败，但提供信息）
    echo "尝试不使用CGO编译（仅用于测试）..."
    export CGO_ENABLED=0
fi

# 创建构建目录
mkdir -p build

# 构建应用
echo "正在编译..."
if [ "$CGO_ENABLED" = "1" ]; then
    # 使用CGO编译（需要Windows工具链）
    export CC=x86_64-w64-mingw32-gcc
    export CXX=x86_64-w64-mingw32-g++
    go build -ldflags="-H windowsgui" -o build/linux-security-checker.exe ./cmd
else
    # 不使用CGO编译（Fyne需要CGO，会失败）
    go build -o build/linux-security-checker.exe ./cmd
fi

if [ $? -eq 0 ]; then
    echo "构建成功！"
    echo "输出文件: build/linux-security-checker.exe"
    ls -la build/linux-security-checker.exe
else
    echo "构建失败！"
    echo ""
    echo "建议在Windows环境下直接编译："
    echo "1. 安装Go 1.21+"
    echo "2. 安装TDM-GCC或MinGW-w64"
    echo "3. 运行: go build -o linux-security-checker.exe ./cmd"
    exit 1
fi
